"use client";
import { useCallback, useContext } from "react";
import { toast } from "sonner";
import { apiClient } from "@/lib/api";
import { SocketContext } from "@/context/socket";
import { validateFile, isImageType } from "@/lib/utils";

interface UseFileUploadProps {
  roomId: string | number;
  currentUserId: string | number;
  senderFullName: string;
  user?: any; // For user-specific data
  onUploadStart?: () => void;
  onUploadEnd?: () => void;
  onFileMessage?: (message: any) => void;
}

export const useFileUpload = ({
  roomId,
  currentUserId,
  senderFullName,
  user,
  onUploadStart,
  onUploadEnd,
  onFileMessage,
}: UseFileUploadProps) => {
  const { socket } = useContext(SocketContext);

  const uploadImage = useCallback(
    async (e: React.ChangeEvent<HTMLInputElement>) => {
      if (!e.target.files) return;

      try {
        const file = e.target.files[0];
        const validation = validateFile(file);

        if (!validation.isValid) {
          toast(validation.error);
          return;
        }

        onUploadStart?.();

        const data = new FormData();
        data.append("file", file);
        if (currentUserId && roomId) {
          data.append("user_id", currentUserId.toString());
          data.append("room_id", roomId.toString());
        }

        const res = await apiClient({
          method: "POST",
          endpoint: "files/upload",
          data,
          isFormData: true,
        });

        const messageData = {
          sender_id: currentUserId,
          message: "ATTACHMENT",
          type: "room",
          file_path: res.fileUrl,
          file_id: res.fileId,
          room_id: roomId,
          sender: {
            user_id: currentUserId,
            name: senderFullName,
          },
        };

        socket?.emit("send-message", messageData);

        const newMessage = {
          message: "ATTACHMENT",
          type: "room",
          file_id: res.fileId,
          file: {
            user_file_id: res.fileId,
            path: res.fileUrl,
          },
          sender: {
            user_id: currentUserId,
            name: senderFullName,
          },
        };

        onFileMessage?.(newMessage);
        toast.success("File uploaded successfully");
      } catch (error) {
        toast.error("Upload Failed");
      } finally {
        onUploadEnd?.();
      }
    },
    [
      roomId,
      currentUserId,
      senderFullName,
      socket,
      onUploadStart,
      onUploadEnd,
      onFileMessage,
    ],
  );

  const handleOnAttached = useCallback(() => {
    const input = document.createElement("input");
    input.type = "file";
    input.accept = "image/*,.pdf,.doc,.docx";
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        uploadImage({
          target: { files: [file] },
        } as unknown as React.ChangeEvent<HTMLInputElement>);
      }
    };
    input.click();
  }, [uploadImage]);

  const getFileSignUrl = useCallback(
    async (file_id: string): Promise<string | null> => {
      onUploadStart?.();

      try {
        const { getAuthToken } = await import("@/lib/tokenManager");
        const token = await getAuthToken();
        const res = await apiClient({
          method: "POST",
          endpoint: "files/sign-url",
          data: { file_id },
          token: token || undefined,
        });

        return res.data.url;
      } catch (error) {
        toast.error("Failed to get file URL");
        return null;
      } finally {
        onUploadEnd?.();
      }
    },
    [onUploadStart, onUploadEnd],
  );

  return {
    uploadImage,
    handleOnAttached,
    getFileSignUrl,
  };
};

export default useFileUpload;
