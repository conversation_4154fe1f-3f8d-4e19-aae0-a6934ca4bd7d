"use client";
import { useEffect, useCallback, useContext } from 'react';
import { SocketContext } from '@/context/socket';
import useNotification from '@/hooks/useNotification';

interface UseSocketMessagesProps {
  roomId: string | number;
  currentUserId: string | number;
  senderFullName: string;
  messageHistory: any[];
  onMessageReceived: (newMessages: any[]) => void;
  onRoomStatusToggle?: (isActive: boolean) => void;
  readAllMessage?: () => void;
}

export const useSocketMessages = ({
  roomId,
  currentUserId,
  senderFullName,
  messageHistory,
  onMessageReceived,
  onRoomStatusToggle,
  readAllMessage
}: UseSocketMessagesProps) => {
  const { socket } = useContext(SocketContext);
  const sendNotification = useNotification();

  // Join room when socket connects
  useEffect(() => {
    if (socket && roomId && currentUserId) {
      socket.emit("join", {
        room_id: roomId,
        user_id: currentUserId,
        name: senderFullName,
      });
    }
  }, [socket, roomId, currentUserId, senderFullName]);

  // Handle incoming messages
  useEffect(() => {
    if (!socket) return;

    const handleReceiveMessage = (data: any) => {
      const newMessage = [...messageHistory, data];
      
      // Get sender name from the received message data
      const senderName =
        data.sender?.first_name && data.sender?.last_name
          ? `${data.sender.first_name} ${data.sender.last_name}`
          : data.sender?.name || "Unknown User";

      sendNotification({
        title: `New Message from ${senderName}`,
        body: data.message,
      });

      readAllMessage?.();
      onMessageReceived(newMessage);
    };

    const handleRoomStatusToggle = (data: any) => {
      const { room_status } = data;
      const isActive = room_status === "active";
      onRoomStatusToggle?.(isActive);
    };

    socket.on("receive-message", handleReceiveMessage);
    socket.on("toggle-room-status", handleRoomStatusToggle);

    return () => {
      socket.off("receive-message", handleReceiveMessage);
      socket.off("toggle-room-status", handleRoomStatusToggle);
    };
  }, [socket, messageHistory, sendNotification, readAllMessage, onMessageReceived, onRoomStatusToggle]);

  // Send message function
  const sendMessage = useCallback(async (message: string) => {
    if (!socket || !message.trim()) return;

    const messageData = {
      sender_id: currentUserId,
      message: message,
      type: "room",
      room_id: roomId,
      sender: {
        user_id: currentUserId,
        name: senderFullName,
      },
    };

    socket.emit("send-message", messageData);

    const newMessage = {
      message: message,
      type: "room",
      roomId: roomId,
      sender: {
        user_id: currentUserId,
        name: senderFullName,
      },
      created_at: new Date().toISOString(),
    };

    onMessageReceived([...messageHistory, newMessage]);
  }, [socket, currentUserId, roomId, senderFullName, messageHistory, onMessageReceived]);

  // Toggle room status (for providers)
  const toggleRoomStatus = useCallback(async (isActive: boolean, token?: string) => {
    if (!socket || !roomId) return;

    try {
      const { apiClient } = await import('@/lib/api');
      
      await apiClient({
        method: "POST",
        endpoint: `room/${roomId}/${isActive ? "enable" : "disable"}`,
        data: {},
        token: token || undefined,
      });

      socket.emit("toggle-room-status", {
        room_id: roomId,
        user_id: currentUserId,
        name: senderFullName,
        room_status: isActive ? "active" : "inactive",
      });

      return true;
    } catch (error) {
      console.error("Failed to toggle room status:", error);
      return false;
    }
  }, [socket, roomId, currentUserId, senderFullName]);

  return {
    sendMessage,
    toggleRoomStatus,
  };
};

export default useSocketMessages;
