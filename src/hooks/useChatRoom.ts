"use client";
import { useState, useEffect, useCallback, useContext } from "react";
import { toast } from "sonner";
import { apiClient } from "@/lib/api";
import { SocketContext } from "@/context/socket";
import { validateFile, isImageType, formatChatMessageDate } from "@/lib/utils";
import useNotification from "@/hooks/useNotification";
import { ChatMetadata, User, UserRole } from "@/types/chat";

interface UseChatRoomProps {
  roomIdentifier: string;
  role: UserRole | string;
  token?: string | null;
  patient: User;
  provider: User;
  room: any;
  isConsult?: boolean;
}

interface ChatRoomState {
  messageHistory: any[];
  isConsultOpen: boolean;
  showPopup: boolean;
  otpModalVisible: boolean;
  imageModalVisible: boolean;
  imageModalValue: string;
  otpModalValue: string;
  loader: boolean;
  wrongChat: boolean;
  disableResendOtp: boolean;
  chatMetadata: ChatMetadata | null;
  participantsPopupVisible: boolean;
  user: User | undefined;
  fullName: string | undefined;
}

export const useChatRoom = ({
  roomIdentifier,
  role,
  token,
  patient,
  provider,
  room,
  isConsult = false,
}: UseChatRoomProps) => {
  const { socket, isConnected } = useContext(SocketContext);
  const sendNotification = useNotification();

  // Derived values
  const providerFullName = provider?.first_name + " " + provider?.last_name;
  const patientFullName = patient?.first_name + " " + patient?.last_name;
  const receiverFullName =
    role === "PROVIDER" ? patientFullName : providerFullName;
  const senderFullName =
    role === "PROVIDER" ? providerFullName : patientFullName;
  const roomId = room?.id;
  const currentUserId = role === "USER" ? patient?.user_id : provider?.user_id;

  // State management
  const [state, setState] = useState<ChatRoomState>({
    messageHistory: [],
    isConsultOpen: true,
    showPopup: false,
    otpModalVisible: false,
    imageModalVisible: false,
    imageModalValue: "",
    otpModalValue: "",
    loader: false,
    wrongChat: false,
    disableResendOtp: true,
    chatMetadata: null,
    participantsPopupVisible: false,
    user: undefined,
    fullName: undefined,
  });

  // Update state helper
  const updateState = useCallback((updates: Partial<ChatRoomState>) => {
    setState((prev) => ({ ...prev, ...updates }));
  }, []);

  // API calls
  const readAllMessage = useCallback(async () => {
    try {
      const { getAuthToken } = await import("@/lib/tokenManager");
      const authToken = await getAuthToken();
      await apiClient({
        method: "GET",
        endpoint: `chat/${roomIdentifier}/read-all-messages`,
        data: {},
        token: authToken || token || undefined,
      });
    } catch (error) {
      console.error("Failed to mark messages as read:", error);
    }
  }, [roomIdentifier, token]);

  const getMessageHistory = useCallback(async () => {
    updateState({ loader: true });
    try {
      const { getAuthToken } = await import("@/lib/tokenManager");
      const authToken = await getAuthToken();

      const res = await apiClient({
        method: "GET",
        endpoint: `chat/${roomIdentifier}/messages`,
        data: {},
        token: authToken || token || undefined,
      });

      if (res.statusCode === 200) {
        toast.success("Chat room initiated.", {
          duration: 1000,
          closeButton: true,
        });

        updateState({
          messageHistory: res.data.messages,
          user: res.data.patient,
          isConsultOpen: res.data.room.active,
          showPopup: !res.data.room.active,
          fullName:
            res.data.provider.first_name + " " + res.data.provider.last_name,
          chatMetadata: res.data.chatMetadata || null,
        });
      }

      if (res.statusCode === 500) {
        toast.error(res.message, {
          duration: 1000,
          closeButton: true,
        });
        updateState({ otpModalVisible: true });
        sendOtp();
      }
    } catch (error: any) {
      if (error?.response?.data?.statusCode === 400) {
        toast.error(error?.response?.data?.message, {
          duration: Infinity,
        });
        updateState({ wrongChat: true });
      } else if (error?.response?.data?.statusCode === 500) {
        const { removeAuthToken } = await import("@/lib/tokenManager");
        await removeAuthToken();
        window.location.reload();
      } else {
        toast.error("Something went wrong.");
      }
    } finally {
      updateState({ loader: false });
    }
  }, [roomIdentifier, token]);

  const sendOtp = useCallback(async () => {
    updateState({ disableResendOtp: true });
    try {
      const res = await apiClient({
        method: "POST",
        endpoint: "auth/send-otp",
        data: { room_identifier: roomIdentifier },
      });

      if (res.statusCode === 200) {
        updateState({ user: res.data?.user, disableResendOtp: false });
        toast.success(res.message);
      } else {
        toast.error(res.message);
        updateState({ disableResendOtp: false });
      }
    } catch (error) {
      toast.error("Failed to send OTP");
      updateState({ disableResendOtp: false });
    }
  }, [roomIdentifier]);

  const verifyUserOtp = useCallback(async () => {
    if (!state.otpModalValue) {
      toast.error("Please enter OTP");
      return;
    }

    try {
      const res = await apiClient({
        method: "POST",
        endpoint: "auth/verify-otp",
        data: { user_id: state.user?.user_guid, otp: state.otpModalValue },
      });

      if (res.statusCode === 200) {
        updateState({ otpModalVisible: false });
        const { setAuthToken } = await import("@/lib/tokenManager");
        await setAuthToken(res.data.token, res.data.user);
        toast.success(res.message);
        getMessageHistory();
      } else {
        toast.error(res.message);
      }
    } catch (error) {
      toast.error("OTP verification failed");
    }
  }, [state.otpModalValue, state.user?.user_guid, getMessageHistory]);

  // Initialize authentication check
  const checkLogin = useCallback(async () => {
    if (!state.isConsultOpen && isConsult) return;

    const { getAuthToken } = await import("@/lib/tokenManager");
    const authToken = await getAuthToken();

    if (!authToken && isConsult) {
      updateState({ otpModalVisible: true });
      sendOtp();
    } else {
      getMessageHistory();
      readAllMessage();
    }
  }, [
    state.isConsultOpen,
    isConsult,
    getMessageHistory,
    readAllMessage,
    sendOtp,
  ]);

  return {
    // State
    ...state,

    // Derived values
    providerFullName,
    patientFullName,
    receiverFullName,
    senderFullName,
    roomId,
    currentUserId,
    isConnected,

    // Actions
    updateState,
    getMessageHistory,
    readAllMessage,
    sendOtp,
    verifyUserOtp,
    checkLogin,

    // Socket reference
    socket,
  };
};

export default useChatRoom;
