"use client";
import React, { Suspense, useEffect, useState } from "react";
import "@chatscope/chat-ui-kit-styles/dist/default/styles.min.css";
import { CheckCircle, RotateCcw, Users } from "lucide-react";
import {
  ChatContainer,
  ConversationHeader,
  Message,
  MessageList,
  Avatar,
  MessageInput,
} from "@chatscope/chat-ui-kit-react";

import { toast } from "sonner";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button, buttonVariants } from "@/components/ui/button";
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSeparator,
  InputOTPSlot,
} from "@/components/ui/input-otp";
import { REGEXP_ONLY_DIGITS_AND_CHARS } from "input-otp";
import { isImageType, formatChatMessageDate } from "@/lib/utils";
import FileDisplayContainer from "../fileDisplayContainer";
import { Loader } from "../shared/loader";
import ParticipantsPopup from "./participants-popup";
import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "../ui/alert-dialog";
import LogoutButton from "@/components/auth/logout-button";
import { Switch } from "../ui/switch";
import { useRouter } from "next/navigation";

// Import our custom hooks
import useChatRoom from "@/hooks/useChatRoom";
import useFileUpload from "@/hooks/useFileUpload";
import useSocketMessages from "@/hooks/useSocketMessages";

interface User {
  email?: string;
  first_name: string;
  last_name: string;
  status?: string;
  user_guid?: string;
  user_id: number | string;
}

interface UnifiedChatProps {
  roomIdentifier: string;
  role: "USER" | "PROVIDER" | string;
  isConsult?: boolean;
  token?: string | null;
  patient: User;
  provider: User;
  room: any;
}

const UnifiedChat = ({
  roomIdentifier,
  role,
  isConsult = false,
  token,
  patient,
  provider,
  room,
}: UnifiedChatProps) => {
  const router = useRouter();

  // Use our custom hooks
  const {
    messageHistory,
    isConsultOpen,
    showPopup,
    otpModalVisible,
    imageModalVisible,
    imageModalValue,
    otpModalValue,
    loader,
    wrongChat,
    disableResendOtp,
    chatMetadata,
    participantsPopupVisible,
    user,
    providerFullName,
    patientFullName,
    receiverFullName,
    senderFullName,
    roomId,
    currentUserId,
    isConnected,
    socket,
    updateState,
    getMessageHistory,
    readAllMessage,
    sendOtp,
    verifyUserOtp,
    checkLogin,
  } = useChatRoom({
    roomIdentifier,
    role,
    token,
    patient,
    provider,
    room,
    isConsult,
  });

  const { handleOnAttached, getFileSignUrl } = useFileUpload({
    roomId: roomId || "",
    currentUserId: currentUserId || "",
    senderFullName: senderFullName || "",
    onUploadStart: () => updateState({ loader: true }),
    onUploadEnd: () => updateState({ loader: false }),
    onFileMessage: (newMessage) => {
      updateState({ messageHistory: [...messageHistory, newMessage] });
    },
  });

  const { sendMessage, toggleRoomStatus } = useSocketMessages({
    roomId: roomId || "",
    currentUserId: currentUserId || "",
    senderFullName: senderFullName || "",
    messageHistory,
    onMessageReceived: (newMessages) =>
      updateState({ messageHistory: newMessages }),
    onRoomStatusToggle: (isActive) => {
      updateState({
        isConsultOpen: isActive,
        showPopup: !isActive,
      });
      if (!isActive) {
        console.log("Chat room closed by provider");
      }
    },
    readAllMessage,
  });

  // Check login on mount
  useEffect(() => {
    const checkLogin = async () => {
      if (!isConsultOpen && isConsult) return;

      const { getAuthToken } = await import("@/lib/tokenManager");
      const authToken = await getAuthToken();

      if (!authToken && isConsult) {
        updateState({ otpModalVisible: true });
        sendOtp();
      } else {
        getMessageHistory();
        readAllMessage();
      }
    };

    checkLogin();
  }, []);

  // Handle message sending
  const handledMessageSend = async (message: string) => {
    await sendMessage(message);
  };

  // Handle file click to get signed URL
  const handleFileClick = async (filePath: string) => {
    const url = await getFileSignUrl(filePath);
    if (url) {
      updateState({ imageModalVisible: true, imageModalValue: url });
    }
  };

  // Handle room toggle (for providers)
  const handleToggleSwitch = async (isActive: boolean) => {
    updateState({ loader: true });
    const success = await toggleRoomStatus(isActive, token || undefined);
    updateState({ loader: false });

    if (!success) {
      toast.error("Something went wrong while toggling switch.");
    }
  };

  // Show provider-specific controls
  const showProviderControls = role === "PROVIDER";

  // Show OTP modal for user chat
  const showOTPModal = isConsult && otpModalVisible;

  return (
    <Suspense>
      <Loader show={loader} />
      <ChatContainer style={{ height: "100vh" }}>
        <ConversationHeader>
          <Avatar
            name={receiverFullName}
            src={`/images/${
              role === "PROVIDER" ? "patient.png" : "provider.png"
            }`}
          />
          <ConversationHeader.Content
            userName={receiverFullName}
            info={
              <div className="text-xs text-gray-500 space-y-1">
                {chatMetadata?.room?.description && (
                  <div>{chatMetadata.room.description}</div>
                )}
                {chatMetadata?.service?.display_service_name && (
                  <div className="font-medium text-blue-600">
                    Service: {chatMetadata.service.display_service_name}
                  </div>
                )}
              </div>
            }
          />
          <ConversationHeader.Actions className="flex items-center gap-2">
            {chatMetadata?.participants && (
              <Button
                variant="ghost"
                size="icon"
                onClick={() => updateState({ participantsPopupVisible: true })}
                title="View Participants"
              >
                <Users className="h-4 w-4" />
              </Button>
            )}

            {showProviderControls && (
              <Switch
                id="chat-switch"
                defaultChecked={room.active}
                onCheckedChange={handleToggleSwitch}
              />
            )}

            {isConnected && <CheckCircle />}
            <LogoutButton variant="ghost" size="icon" showText={false} />
          </ConversationHeader.Actions>
        </ConversationHeader>

        <MessageList>
          {messageHistory?.map((data, idx) => {
            const isSendedByMe = data.sender.user_id === currentUserId;
            const fullName =
              data.sender?.first_name && data.sender?.last_name
                ? `${data.sender.first_name} ${data.sender.last_name}`
                : data.sender?.name || "Unknown";
            const formattedTime = formatChatMessageDate(data.created_at);

            return (
              <Message
                key={idx}
                onClick={() => {
                  if (data.file?.path) {
                    handleFileClick(data.file.path);
                  }
                }}
                model={{
                  direction: isSendedByMe ? "outgoing" : "incoming",
                  position: "last",
                  sender: senderFullName,
                  sentTime: data?.created_at,
                  type: "html",
                }}
              >
                {data?.message === "ATTACHMENT" ? (
                  <Message.ImageContent
                    className="cursor-pointer"
                    src={`/images/${
                      isImageType(data.file?.path) ? "file.png" : "pdf.png"
                    }`}
                    height={100}
                  />
                ) : (
                  <Message.HtmlContent
                    html={`<b>${fullName}: </b> <br/>${data?.message} <br/><em style='font-size:10px'>${formattedTime}</em>`}
                  />
                )}
                {!isSendedByMe && (
                  <Avatar
                    name="User"
                    src={`/images/${
                      role === "PROVIDER" ? "patient.png" : "provider.png"
                    }`}
                  />
                )}
              </Message>
            );
          })}
        </MessageList>

        {(isConsultOpen || !isConsult) && (
          <MessageInput
            autoFocus
            placeholder="Type message here"
            onSend={handledMessageSend}
            onAttachClick={handleOnAttached}
          />
        )}
      </ChatContainer>

      {/* OTP Modal for user authentication */}
      {showOTPModal && (
        <Dialog open={otpModalVisible}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Enter OTP</DialogTitle>
              <DialogDescription>
                Enter the OTP sent to your email.
              </DialogDescription>
            </DialogHeader>
            <div className="flex flex-col gap-4 py-4 justify-center items-center">
              <InputOTP
                maxLength={6}
                value={otpModalValue}
                pattern={REGEXP_ONLY_DIGITS_AND_CHARS}
                onChange={(value) => updateState({ otpModalValue: value })}
              >
                <InputOTPGroup>
                  <InputOTPSlot index={0} />
                  <InputOTPSlot index={1} />
                  <InputOTPSlot index={2} />
                </InputOTPGroup>
                <InputOTPSeparator />
                <InputOTPGroup>
                  <InputOTPSlot index={3} />
                  <InputOTPSlot index={4} />
                  <InputOTPSlot index={5} />
                </InputOTPGroup>
              </InputOTP>
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                disabled={disableResendOtp}
                onClick={sendOtp}
              >
                <RotateCcw className="mr-2 h-4 w-4" /> Resend OTP
              </Button>
              <Button variant="default" type="submit" onClick={verifyUserOtp}>
                Submit
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}

      {/* File Display Modal */}
      <FileDisplayContainer
        src={imageModalValue}
        imageModalVisible={imageModalVisible}
        setImageModalVisible={(visible) =>
          updateState({ imageModalVisible: visible })
        }
      />

      {/* Consultation Closed Alert */}
      <AlertDialog open={showPopup}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              Consultation Chat Session Closed
            </AlertDialogTitle>
            <AlertDialogDescription>
              The chat session has been closed by the provider. You can not send
              any more messages in this chat. If you have any further questions
              or concerns, please contact the support team.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel
              onClick={() => updateState({ showPopup: false })}
              className={buttonVariants({ variant: "default" })}
            >
              Okay
            </AlertDialogCancel>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Wrong Chat Alert */}
      <AlertDialog open={wrongChat}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Wrong Chat Link!!!</AlertDialogTitle>
            <AlertDialogDescription>
              You open a wrong chat. Please check the link and open correct
              Link.
            </AlertDialogDescription>
          </AlertDialogHeader>
        </AlertDialogContent>
      </AlertDialog>

      {/* Participants Popup */}
      {chatMetadata?.participants && (
        <ParticipantsPopup
          isOpen={participantsPopupVisible}
          onClose={() => updateState({ participantsPopupVisible: false })}
          participants={chatMetadata.participants}
        />
      )}
    </Suspense>
  );
};

export default UnifiedChat;
